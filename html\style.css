@font-face {
    font-family: OxaniumBold;
    src: url(fonts/Oxanium-Bold.ttf);
}

@font-face {
    font-family: OxaniumSemiBold;
    src: url(fonts/Oxanium-SemiBold.ttf);
}

@font-face {
    font-family: OxaniumLight;
    src: url(fonts/Oxanium-Light.ttf);
}

body{
    overflow: hidden;
    display: none;
}

.effect{
    position: absolute;
    top:0vw;
    left:0vw;
    width: 100%;
    height: 100%;
    z-index: -5;
}

.kopya{
    position: absolute;
    top: 0vw;
    left: 0vw;
    width: 100%;
    height: 100%;
    opacity: 0.7;
    display: none;
}

.title{
    position: absolute;
    font-family: OxaniumSemiBold;
    color:white;
    font-size: 1.7vw;
    top:0.2vw;
    left:1.85vw;
    text-shadow: 0px 0px 0.5vw rgba(255, 255, 255, 0.48);
}

.subTitle{
    position: absolute;
    font-family: OxaniumSemiBold;
    font-size: 1.7vw;
    top:2vw;
    left:1.85vw;

    color: #0C7CFF;
    text-shadow: 0px 0px 0.5VW rgba(12, 124, 255, 0.63);
}

.description{
    position: absolute;
    width: 13vw;
    font-family: OxaniumLight;
    font-size: 0.8vw;
    top:5.5vw;
    left:2.1vw;

    color: rgba(255, 255, 255, 0.72);
}

.stick{
    position: absolute;
    width: 18.4vw;
    height: 2.91vw;
    top:8.65vw;
    left:1.9vw;
}

#categoryArea{
    position: absolute;
    width: 7vw;
    height: 41vw;
    left:1.5vw;
    top:15vw;
    overflow-y: scroll;
}

#categoryArea::-webkit-scrollbar {
    width: 0vw;
}

.categoryImage{
    position: absolute;
    width: 5.7vw;
    height: 5.7vw;
    left:0.5vw;
    top: 0.1vw;
}

.categoryTitle{
    position: absolute;
    width: 5.62vw;
    left:0.5vw;
    top:-0.35vw;
    font-size: 0.8vw;
    font-family: OxaniumLight;
    color: rgba(255, 255, 255, 0.72);
    text-align: center;
}

.category{
    position: relative;
    width: 5.7vw;
    height: 5.7vw;
    left:0vw;
    top: -1.1vw;
    margin-top: 1.1vw;
    opacity: 0.6;
}

#vehicleArea{
    position: absolute;
    width: 11.5vw;
    height: 41vw;
    left:8.5vw;
    top:15vw;
    overflow-y: scroll;
}

#vehicleArea::-webkit-scrollbar {
    width: 0vw;
}

.vehicleBox{
    position: absolute;
    width: 10.5vw;
    height: 5.7vw;
    left:0.2vw;
    top: 0.1vw;
}

.vehicleImage{
    position:absolute;
    left:4.1vw;
    top: 1.5vw;
    width:6vw;
    height:4vw !important;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
    background-image: url("./img/Ambulance.png");
    overflow:hidden;
    filter: drop-shadow(0px 0.2vw 0.5vw #000000);
}

.vehicleText{
    position: absolute;
    width: 5.62vw;
    left:0.8vw;
    top:2.64vw;
    font-size: 0.95vw;
    font-family: OxaniumLight;
    color: rgba(255, 255, 255, 0.72);

    overflow: hidden; /* taşanları gizle */
    white-space: nowrap; /* alt satıra hiç inme */
    text-overflow: ellipsis; /* eğer uzunsa üç nokta koy */
}

.vehiclePrice{
    position: absolute;
    width: 5.62vw;
    left:0.8vw;
    top:3.8vw;
    font-size: 0.8vw;
    font-family: OxaniumLight;
    color: #59FF60;
}

.vehicle{
    position: relative;
    width: 5.7vw;
    height: 5.7vw;
    left:0vw;
    top: -1.1vw;
    margin-top: 1.1vw;
    opacity: 0.6;
}

.active{
    opacity: 1;
}

.titleRight{
    position: absolute;
    font-family: OxaniumSemiBold;
    color:white;
    font-size: 1.7vw;
    top:0.2vw;
    right:10.5vw;
    text-shadow: 0px 0px 0.5vw rgba(255, 255, 255, 0.48);
}

.subTitleRight{
    position: absolute;
    font-family: OxaniumSemiBold;
    font-size: 1.7vw;
    top:2vw;
    right:5.8vw;

    color: #FF275B;
    text-shadow: 0px 0px 0.5VW rgba(255, 39, 91, 0.63);
}

.descriptionRight{
    position: absolute;
    width: 13vw;
    font-family: OxaniumLight;
    font-size: 0.8vw;
    top:5.5vw;
    right:4.1vw;

    color: rgba(255, 255, 255, 0.72);
}

.vehicleName{
    position: absolute;
    font-family: OxaniumLight;
    color:white;
    font-size: 2vw;
    width: 12.8vw;
    text-align: center;
    top:8.9vw;
    right:4.1vw;

    overflow: hidden; /* taşanları gizle */
    white-space: nowrap; /* alt satıra hiç inme */
    text-overflow: ellipsis; /* eğer uzunsa üç nokta koy */
}

.vehiclePriceText{
    position: absolute;
    font-family: OxaniumLight;
    color: #59FF60;
    font-size: 1.3vw;
    width: 13.1vw;
    text-align: center;
    top:11.5vw;
    right:4.1vw;
}

.rightTitle{
    position: absolute;
    font-family: OxaniumLight;
    color:white;
    font-size: 1.3vw;
    width: 13vw;
    text-align: center;
    top:13.3vw;
    right:4.1vw;
}

.pageStick{
    position: absolute;
    width: 12vw;
    height: 0.15vw;
    top:16.1vw;
    right:5vw
}

#infoArea{
    position: absolute;
    width: 13vw;
    height: 17.7vw;

    top:17vw;
    right: 4.3vw;
}

.info{
    position: relative;
    width: 13vw;
    height: 3vw;

    top:0.4vw;
    right: 0vw;

    margin-top: -0.15vw;
}

.infoTitle{
    position: absolute;
    font-family: OxaniumLight;
    color:white;
    font-size: 0.9vw;
    width: 13vw;
    top:-0.8vw;
    right: -0.5vw;
}

.infoValue{
    position: absolute;
    font-family: OxaniumLight;
    color:white;
    font-size: 0.75vw;
    width: 13vw;
    text-align: center;
    top:-0.4vw;
    right: -4.8vw;
}

.valueBox{
    position: relative;
    width: 1vw;
    height: 1.5vw;
    top:1.26vw;
    left:0.27vw;
    
    background: #1B1C1B;
    float: left;
    margin-left: 0.24vw;
}

.activeValue{
    background: radial-gradient(50% 50% at 50% 50%, #FF275B 0%, rgba(255, 39, 91, 0.62) 100%);
}

.color{
    top:33.6vw
}

.colorStick{
    top:36.4vw
}

#colorArea{
    position: absolute;
    width: 13.1vw;
    height: 6.7vw;
    top:37vw;
    right: 4.25vw;
}

.colorPalete{
    position: relative;
    width: 1.5vw;
    height: 1.4vw;

    background: orange;
    border: 0.1vw solid #FFFFFF;
    top:0.05vw;
    left:0.35vw;
    float: left;

    margin-left: 0.3vw;
    margin-top: 0.3vw;
}

#colorInput{
    position: absolute;
    border: none;
    background: transparent;
    width: 12.15vw;
    height: 2.38vw;

    top:41vw;
    right: 4.8vw;
}

.purchase{
    top:42.8vw
}

.purchaseStick{
    top:45.6vw
}

.totalPrice{
    position: absolute;
    font-family: OxaniumLight;
    color:white;
    font-size: 0.75vw;
    width: 13vw;
    text-align: center;
    top:46vw;
    right: 7vw;
}

.total{
    position: absolute;
    font-family: OxaniumSemibold;
    color:white;
    font-size: 1.85vw;
    text-align: center;
    top:45.94vw;
    right: 9.9vw;

    color: #FF275B;
    text-shadow: 0px 0px 0.5vw rgba(255, 39, 91, 0.5);
}

.card{
    position: absolute;
    width: 2.4vw;
    height: 2.4vw;
    right: 6.8vw;
    top:47.2vw
}

.cash{
    position: absolute;
    width: 2.4vw;
    height: 2.4vw;
    right: 3.7vw;
    top:47.2vw
}

.testDrive{
    position: absolute;
    width: 12.9vw;
    height: 2.1vw;
    right: 3.7vw;
    top:49vw;

    line-height: 2.2vw;

    background: radial-gradient(48.57% 48.57% at 48.57% 51.43%, rgba(255, 39, 91, 0.54) 0%, rgba(255, 39, 91, 0.41) 100%);
    border: 0.1vw solid rgba(255, 0, 61, 0.48);

    font-family: OxaniumSemibold;
    color:white;
    font-size: 1.15vw;
    text-align: center;
}

.input{
    position: absolute;
    width: 17.1vw;
    height: 2.3vw;

    top: 11.5vw;
    left: 2vw;

    border-radius: 0.2vw;
    text-align: left;
    font-family: "Montserrat", sans-serif;
    font-size: 0.9vw;
    padding-left: 1.2vw;
    background: rgba(255, 255, 255, 0.02);    border: none;
    outline: none;
    z-index: 999;
    color: #fff;
}

.search{
    position: absolute;
    width: 1vw;
    height: 1.1vw;
    top:12.2vw;
    left:17.2vw;
}

.rightMouse{
    position: absolute;
    width: 1.5vw;
    height: 1.8vw;
    top: 7.5vw;
    left: -0.1vw;
}

.leftMouse{
    position: absolute;
    width: 1.5vw;
    height: 1.8vw;
    top: 10.2vw;
    left: -0.1vw;
}

.rotateRight{
    position: absolute;
    width: 6.1vw;
    height: 1.3vw;
    top: 6.5vw;
    left: 2.2vw;
    color: white;
    font-size: 0.9vw;
    font-family: OxaniumSemiBold;
}

.rotateDescription{
    position: absolute;
    font-size: 0.7vw;
    font-family: OxaniumLight;
    color: rgba(255, 255, 255, 0.37);
    top: 7.9vw;
    left: 2.2vw;
}

.rotateLeft{
    position: absolute;
    width: 6.1vw;
    height: 1.3vw;
    top: 9.4vw;
    left: 2.2vw;
    color: white;
    font-size: 0.9vw;
    font-family: OxaniumSemiBold;
}

.rotateLeftDescription{
    position: absolute;
    font-size: 0.7vw;
    font-family: OxaniumLight;
    color: rgba(255, 255, 255, 0.37);
    top: 10.75vw;
    left: 2.2vw;
}

#rightMouse{
    position:absolute;
    width:20vw;
    bottom:10vw;
    left:55vw
}

#leftMouse{
    position:absolute;
    width:20vw;
    bottom:12.8vw;
    left:40vw
}

#rotateCheck{
    position:absolute;
    width:100%;
    height:100%;
}

img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

*:not(input):not(textarea) {
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
    -ms-user-select: none;
    user-select: none
}
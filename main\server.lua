local QBCore = exports['qb-core']:GetCoreObject()

QBCore.Functions.CreateCallback('stg_vehicleshop:getMoney', function(source, cb, payType, amount)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return cb(false) end

    if payType == "cash" then
        if Player.PlayerData.money.cash >= amount and amount > 0 then
            Player.Functions.RemoveMoney("cash", amount)
            cb(true)
        else
            cb(false)
        end
    elseif payType == "bank" then
        if Player.PlayerData.money.bank >= amount and amount > 0 then
            Player.Functions.RemoveMoney("bank", amount)
            cb(true)
        else
            cb(false)
        end
    else
        cb(false)
    end
end)

RegisterNetEvent('stg_vehicleshop:buyVehicle')
AddEventHandler('stg_vehicleshop:buyVehicle', function(vehicle)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end

    exports.oxmysql:insert('INSERT INTO player_vehicles (license, citizenid, vehicle, hash, mods, plate, garage) VALUES (?, ?, ?, ?, ?, ?, ?)', {
        Player.PlayerData.license,
        Player.PlayerData.citizenid,
        vehicle.model,
        GetHashKey(vehicle.model),
        json.encode(vehicle),
        vehicle.plate,
        'pillboxgarage'
    }, function(id)
        if id then
            TriggerClientEvent('stg_vehicleshop:buyVehicle', src, vehicle)
        else
            TriggerClientEvent('QBCore:Notify', src, 'خطا در ثبت ماشین!', 'error')
        end
    end)
end)

QBCore.Functions.CreateCallback('stg_vehicleshop:isPlateTaken', function(source, cb, plate)
    exports.oxmysql:scalar('SELECT plate FROM player_vehicles WHERE plate = ?', {plate}, function(result)
        cb(result ~= nil)
    end)
end)

RegisterNetEvent('stg_vehicleshop:setVehicleGang')
AddEventHandler('stg_vehicleshop:setVehicleGang', function(vehicleProps, society)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end

    if Player.PlayerData.permission_level and Player.PlayerData.permission_level >= 1 then
        exports.oxmysql:insert('INSERT INTO player_vehicles (license, citizenid, vehicle, hash, mods, plate, garage) VALUES (?, ?, ?, ?, ?, ?, ?)', {
            society,
            society,
            vehicleProps.model,
            GetHashKey(vehicleProps.model),
            json.encode(vehicleProps),
            vehicleProps.plate,
            'pillboxgarage'
        }, function(id)
            if id then
                TriggerClientEvent('QBCore:Notify', src, 'Vehicle added with plate ' .. vehicleProps.plate, 'success')
            else
                TriggerClientEvent('QBCore:Notify', src, 'Error registering vehicle!', 'error')
            end
        end)
    else
        TriggerClientEvent('stg_vehicleshop:deleteVehicle', src)
        print(('stg_vehicleshop: %s attempted to inject vehicle!'):format(Player.PlayerData.citizenid))
    end
end)

RegisterNetEvent('stg_vehicleshop:setVehicleGangHeli')
AddEventHandler('stg_vehicleshop:setVehicleGangHeli', function(vehicleProps, society)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end

    if Player.PlayerData.permission_level and Player.PlayerData.permission_level >= 1 then
        exports.oxmysql:insert('INSERT INTO owned_vehicles (owner, plate, vehicle, job, type) VALUES (?, ?, ?, ?, ?)', {
            society,
            vehicleProps.plate,
            json.encode(vehicleProps),
            'gang',
            'heli'
        }, function(id)
            if id then
                TriggerClientEvent('QBCore:Notify', src, 'Helicopter added with plate ' .. vehicleProps.plate, 'success')
            else
                TriggerClientEvent('QBCore:Notify', src, 'Error registering helicopter!', 'error')
            end
        end)
    else
        TriggerClientEvent('stg_vehicleshop:deleteVehicle', src)
        print(('stg_vehicleshop: %s attempted to inject vehicle!'):format(Player.PlayerData.citizenid))
    end
end)

RegisterNetEvent('stg_vehicleshop:AdminSetVehicleOwnedPlayerId')
AddEventHandler('stg_vehicleshop:AdminSetVehicleOwnedPlayerId', function(playerId, vehicleProps)
    local src = source
    local AdminPlayer = QBCore.Functions.GetPlayer(src)
    local TargetPlayer = QBCore.Functions.GetPlayer(playerId)

    if not AdminPlayer or not TargetPlayer then return end

    if AdminPlayer.PlayerData.permission_level and AdminPlayer.PlayerData.permission_level >= 1 then
        exports.oxmysql:insert('INSERT INTO player_vehicles (license, citizenid, vehicle, hash, mods, plate, garage) VALUES (?, ?, ?, ?, ?, ?, ?)', {
            TargetPlayer.PlayerData.license,
            TargetPlayer.PlayerData.citizenid,
            vehicleProps.model,
            GetHashKey(vehicleProps.model),
            json.encode(vehicleProps),
            vehicleProps.plate,
            'pillboxgarage'
        }, function(id)
            if id then
                TriggerClientEvent('QBCore:Notify', playerId, "Vehicle added with plate " .. vehicleProps.plate, 'success')
            else
                TriggerClientEvent('QBCore:Notify', src, 'Error registering vehicle!', 'error')
            end
        end)
    end
end)

RegisterNetEvent('stg_vehicleshop:AdminSetVehicleOwnedPlayerIdOff')
AddEventHandler('stg_vehicleshop:AdminSetVehicleOwnedPlayerIdOff', function(steamId, vehicleProps)
    local src = source
    local AdminPlayer = QBCore.Functions.GetPlayer(src)

    if not AdminPlayer then return end

    if AdminPlayer.PlayerData.permission_level and AdminPlayer.PlayerData.permission_level >= 1 then
        exports.oxmysql:insert('INSERT INTO owned_vehicles (owner, plate, vehicle) VALUES (?, ?, ?)', {
            steamId,
            vehicleProps.plate,
            json.encode(vehicleProps)
        }, function(id)
            if id then
                TriggerClientEvent('QBCore:Notify', src, "Vehicle added with plate " .. vehicleProps.plate, 'success')
            else
                TriggerClientEvent('QBCore:Notify', src, 'Error registering vehicle!', 'error')
            end
        end)
    end
end)

RegisterNetEvent('stg_vehicleshop:ChangeVehiclePlate')
AddEventHandler('stg_vehicleshop:ChangeVehiclePlate', function(vehicleProps, oldPlate)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end

    if Player.PlayerData.permission_level and Player.PlayerData.permission_level >= 1 then
        exports.oxmysql:execute('UPDATE player_vehicles SET mods = ?, plate = ? WHERE plate = ?', {
            json.encode(vehicleProps),
            vehicleProps.plate,
            oldPlate
        }, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Vehicle plate changed to ' .. vehicleProps.plate, 'success')
            else
                TriggerClientEvent('QBCore:Notify', src, 'Error changing plate!', 'error')
            end
        end)
    end
end)

RegisterNetEvent('stg_vehicleshop:DeleteVehicle')
AddEventHandler('stg_vehicleshop:DeleteVehicle', function(oldPlate)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end

    if Player.PlayerData.permission_level and Player.PlayerData.permission_level >= 1 then
        exports.oxmysql:execute('DELETE FROM player_vehicles WHERE plate = ?', {oldPlate}, function(affectedRows)
            if affectedRows > 0 then
                TriggerClientEvent('QBCore:Notify', src, 'Vehicle with plate ' .. oldPlate .. ' successfully deleted', 'success')
            else
                TriggerClientEvent('QBCore:Notify', src, 'Error deleting vehicle!', 'error')
            end
        end)
    end
end)

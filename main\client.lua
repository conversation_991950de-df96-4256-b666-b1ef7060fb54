local sleep = 0
local lastShop = false
local cam = nil
local lastVehicle = nil
local lastPrice = nil
local lastLocation = nil

local QBCore = exports['qb-core']:GetCoreObject()

-- Citizen Threads

CreateThread(function()
    for k,v in pairs(STG.Shops) do
        local blipInfo = v["blip"]
        local blip = AddBlipForCoord(STG.Shops[k]["npc"]["coord"])
        SetBlipSprite(blip, blipInfo.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, blipInfo.scale)
        SetBlipColour(blip, blipInfo.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(blipInfo.label)
        EndTextCommandSetBlipName(blip)
    end
end)

CreateThread(function()
    for k,v in pairs(STG.Shops) do
        local model = GetHashKey(v.npc["model"])
        RequestModel(model)
        while not HasModelLoaded(model) do Wait(1) end
        local ped = CreatePed(4, model, v.npc["coord"], false)
        FreezeEntityPosition(ped, true)
        SetBlockingOfNonTemporaryEvents(ped, true)
        SetEntityInvincible(ped, true)
    end
end)

CreateThread(function()
    while true do
        find = false
        for k,v in pairs(STG.Shops) do
            local coords = vector3(v.npc.coord.x, v.npc.coord.y, v.npc.coord.z+1)
            if #(coords - GetEntityCoords(PlayerPedId())) < 13.0 then
                distance = #(coords - GetEntityCoords(PlayerPedId()))
                sleep = 0
                find = true
                targetShop = k
                lastShop = k
            end
        end
        if not find then targetShop = nil sleep = 3000 end
        Wait(3000)
    end
end)

CreateThread(function()
    while true do
        Wait(sleep)
        if targetShop and distance < 2.0 then
            leftNotify(getMessage("open"), true, false, 2000)
            if IsControlJustPressed(0, 38) then openMenu() end
        end
    end
end)

RegisterNUICallback("exit", function()
    deleteCamera()
    SetNuiFocus(false, false)
    DeleteVehicle(lastVehicle)
end)

RegisterNUICallback("loadCategory", function(data)
    for k,v in pairs(STG.Vehicles[data.id]) do
        SendNUIMessage({ type = "addVehicle", model = v.modelName, label = GetLabelText(GetDisplayNameFromVehicleModel(v.modelName)), price = v.price, available = STG.Shops[lastShop].testDrive })
    end
end)

RegisterNUICallback("rotate", function(data)
    local myHeading = GetEntityHeading(lastVehicle)
    if data.status == "left" then myHeading = myHeading - 20.0 else myHeading = myHeading + 20.0 end
    SetEntityHeading(lastVehicle, myHeading)
end)

RegisterNUICallback("changeColor", function(data)
    if data.manuel then
        SetVehicleCustomPrimaryColour(lastVehicle, data.r, data.g, data.b)
    else
        local color = STG.Colors[data.id]
        SetVehicleCustomPrimaryColour(lastVehicle, color.r, color.g, color.b)
    end
end)

RegisterNUICallback("buy", function(data)
    QBCore.Functions.TriggerCallback('stg_vehicleshop:getMoney', function(verify)
        if verify then
            notify('success')
            SetVehicleNumberPlateText(lastVehicle, GeneratePlate())
            TriggerServerEvent('stg_vehicleshop:buyVehicle', QBCore.Functions.GetVehicleProperties(lastVehicle))
        else
            notify('noMoney')
        end
    end, data.type, tonumber(lastPrice))
end)

RegisterNUICallback("testDrive", function(data)
    if data.available then testDrive() else notify("notAvailable") end
end)

RegisterNUICallback("loadVehicle", function(data)
    local model = data.vehicle
    local pmult, tmult = 1000, 800
    local stats = GetPerformanceStats(model)
    local vehicleMaxSpeed = math.ceil(GetVehicleModelEstimatedMaxSpeed(model) * 3.605936) - 100
    local breaking = math.min(GetVehicleModelMaxBraking(model) * 0.9650553, 1.0)
    local acceleration = math.min(GetVehicleModelAcceleration(model) * 2.6, 1.0)

    SendNUIMessage({
        type = "loadVehicle",
        label = GetLabelText(GetDisplayNameFromVehicleModel(model)),
        price = data.price,
        maxSpeed = vehicleMaxSpeed,
        breaking = breaking,
        torque = math.ceil(GetVehicleModelAcceleration(model) * tmult),
        power = math.ceil(GetVehicleModelAcceleration(model) * pmult),
        acceleration = acceleration,
        handling = stats.handling,
        seat = GetVehicleModelNumberOfSeats(model)
    })
    lastPrice = data.price
    viewVehicle(GetHashKey(model))
end)

function hide()
    deleteCamera()
    SetNuiFocus(false, false)
    SendNUIMessage({ type = "hide" })
end

function openMenu()
    lastLocation = GetEntityCoords(PlayerPedId())
    loadColor()
    loadCategory()
    openCamera(STG.Shops[targetShop].camera)
    SendNUIMessage({ type = "open" })
    SetNuiFocus(true, true)
end

function loadCategory()
    for k,v in pairs(STG.Categories) do
        if v.type == lastShop then
            SendNUIMessage({ type = "addCategory", name = k, title = v.title, image = v.image })
        end
    end
end

function show()
    openCamera(STG.Shops[lastShop].camera)
    SetNuiFocus(true, true)
    SendNUIMessage({ type = "open" })
end

function testDrive()
    hide()
    local vehicle = QBCore.Functions.GetVehicleProperties(lastVehicle)
    testVehicle = CreateVehicle(vehicle.model, STG.Shops[lastShop].testDriveCoord, false, false)
    SetVehicleDirtLevel(testVehicle, 0)
    SetPedIntoVehicle(PlayerPedId(), testVehicle, -1)

    SetEntityAsMissionEntity(testVehicle, true, false)
    SetVehicleHasBeenOwnedByPlayer(testVehicle, true)
    SetVehicleNeedsToBeHotwired(testVehicle, false)
    SetVehRadioStation(testVehicle, 'OFF')
    SetModelAsNoLongerNeeded(model)

    QBCore.Functions.SetVehicleProperties(testVehicle, vehicle)
    local count = tonumber(tostring(STG.testDriveTime) .. "00")
    while count > 1 do
        count = count - 1
        local timeSeconds = count / 100
        local timeMinutes = math.floor(timeSeconds / 60.0)
        timeSeconds = timeSeconds - 60.0 * timeMinutes
        Draw2DText(0.015, 0.725, (getMessage("testDrive") .. " \n ~y~%02d:%06.3f"):format(timeMinutes, timeSeconds), 0.7)
        Wait(1)
        if GetVehiclePedIsIn(PlayerPedId()) == 0 then finishTestDrive() break end
    end
    finishTestDrive()
end

function finishTestDrive()
    DeleteVehicle(testVehicle)
    show()
end

function viewVehicle(model)
    DeleteVehicle(lastVehicle)
    loadVehicle(model)
    local spawnCoord = STG.Shops[lastShop].carSpawnCoord
    lastVehicle = CreateVehicle(model, spawnCoord, false, false)
    SetVehicleDirtLevel(lastVehicle, 0)
    SetPedIntoVehicle(PlayerPedId(), lastVehicle, -1)

    SetEntityAsMissionEntity(lastVehicle, true, false)
    SetVehicleHasBeenOwnedByPlayer(lastVehicle, true)
    SetVehicleNeedsToBeHotwired(lastVehicle, false)
    SetVehRadioStation(lastVehicle, 'OFF')
    SetModelAsNoLongerNeeded(model)
    RequestCollisionAtCoord(spawnCoord.x, spawnCoord.y, spawnCoord.z)
end

function loadVehicle(model)
    RequestModel(model)
    while not HasModelLoaded(model) do Wait(1) end
end

function GetPerformanceStats(vehicle)
    local handling1 = GetVehicleModelMaxBraking(vehicle)
    local handling2 = GetVehicleModelMaxBrakingMaxMods(vehicle)
    local handling3 = GetVehicleModelNumberOfSeats(vehicle)
    return { brakes = handling1, handling = (handling1 + handling2) * handling3 }
end

function loadColor()
    for k,v in pairs(STG.Colors) do
        SendNUIMessage({ type = "addColor", id = k, color = v.r .. "," .. v.g .. "," .. v.b })
    end
end

function openCamera(camera)
    SetEntityVisible(PlayerPedId(), 0)
    cam = CreateCam("DEFAULT_SCRIPTED_CAMERA", 0)
    SetCamCoord(cam, camera.camCoord)
    SetCamRot(cam, camera.camRotation, 2)
    SetCamActive(cam, true)
    RenderScriptCams(true, true, 1)
    SetNuiFocus(1, 1)
    DisplayRadar(0)
    SetEntityCoords(PlayerPedId(), camera.camCoord)
end

function deleteCamera()
    if DoesCamExist(cam) then
        DestroyCam(cam, true)
        RenderScriptCams(false, true, 1)
        cam = nil
        SetEntityCoords(PlayerPedId(), lastLocation)
        SetEntityVisible(PlayerPedId(), 1)
        DisplayRadar(1)
    end
end

RegisterNetEvent('stg_vehicleshop:buyVehicle', function (props)
    SendNUIMessage({ type = "exit" })
    Wait(1000)
    loadVehicle(props.model)
    local vehicle = CreateVehicle(props.model, STG.Shops[lastShop].vehicleDeliveryCoord, true, false)
    SetVehicleDirtLevel(lastVehicle, 0)
    QBCore.Functions.SetVehicleProperties(vehicle, props)
    SetPedIntoVehicle(PlayerPedId(), vehicle, -1)
end)

function Draw2DText(x, y, text, scale)
    SetTextFont(4)
    SetTextProportional(7)
    SetTextScale(scale, scale)
    SetTextColour(255, 255, 255, 255)
    SetTextDropShadow(0, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextEdge(4, 0, 0, 0, 255)
    SetTextOutline()
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(x, y)
end

-- Plate Generation
local NumberCharset = {}
local Charset = {}
for i = 48, 57 do table.insert(NumberCharset, string.char(i)) end
for i = 65, 90 do table.insert(Charset, string.char(i)) end
for i = 97, 122 do table.insert(Charset, string.char(i)) end

function GeneratePlate()
    local generatedPlate
    while true do
        Wait(0)
        math.randomseed(GetGameTimer())
        generatedPlate = string.upper(GetRandomLetter(3) .. GetRandomNumber(3))
        local isAvailable = promise.new()
        QBCore.Functions.TriggerCallback('stg_vehicleshop:isPlateTaken', function(isTaken)
            isAvailable:resolve(not isTaken)
        end, generatedPlate)
        if Citizen.Await(isAvailable) then break end
    end
    return generatedPlate
end

function GetRandomNumber(length)
    Wait(0)
    if length > 0 then
        return GetRandomNumber(length - 1) .. NumberCharset[math.random(1, #NumberCharset)]
    else
        return ''
    end
end

function GetRandomLetter(length)
    Wait(0)
    if length > 0 then
        return GetRandomLetter(length - 1) .. Charset[math.random(1, #Charset)]
    else
        return ''
    end
end

RegisterNetEvent('stg_vehicleshop:deleteVehicle', function()
    DeleteVehicle(GetVehiclePedIsIn(PlayerPedId(), false))
end)

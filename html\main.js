var firstCategory = false
var firstVehicle = false
var isAvailable = false

$(function() {
    window.addEventListener('message', function(event) {
        var data = event.data   
        if(data.type == "addCategory" ) {
            let div = `<div onclick="selectCategory('`+data.name+`')" class="category category_`+data.name+`">
            <p class="categoryTitle">`+data.title+`</p>
            <img class="categoryImage" src="images/categories/`+data.image+`">
            </div>`
            $('#categoryArea').append(div)
            if(!firstCategory) {
                selectCategory(data.name)
                firstCategory = true
            }
        }
        if(data.type == "addVehicle" ) {
            let div = `<div onclick="selectVehicle('`+data.model+`', '`+data.price+`', `+data.available+`)" class="vehicle vehicle_`+data.model+`">
            <img src="images/vehicleBox.png" class="vehicleBox">
            <img src="images/vehicles/`+data.model+`.webp" class="vehicleImage">
            <p class="vehicleText">`+data.label+`</p>
            <p class="vehiclePrice">`+data.price+`</p>
            </div>`
            $('#vehicleArea').append(div)
            if(!firstVehicle) {
                selectVehicle(data.model, data.price, data.available)
                firstVehicle = true
            }
        }
        if(data.type == "loadVehicle" ) {
            $('.vehicleName').text(data.label)
            $('.vehiclePriceText').text(data.price)
            $('.total').text(data.price)

            loadMaxSpeed(data.maxSpeed)
            loadBreaking(data.breaking)
            loadTorque(data.torque)
            loadPower(data.power)
            loadAcceleration(data.acceleration)
            loadSeat(data.seat)
        }
        if(data.type == "open" ) {
            $('body').show()
        }
        if(data.type == "exit" ) {
            exit()
        }
        if(data.type == "hide" ) {
            $('body').hide()
        }
        if(data.type == "addColor" ) {
            let div = `<div onclick="changeColor(`+data.id+`)" class="colorPalete color_`+data.id+`"></div>`
            $('#colorArea').append(div)
            $('.color_'+data.id).css('background', "radial-gradient(50% 50% at 50% 50%, rgb("+data.color+") 0%, rgba("+data.color+", 0.62) 100%)")
        }
    })
})

function changeColor(id) {
    $.post('http://stg_vehicleshop/changeColor', JSON.stringify({
        id: id
    }));
}

function loadMaxSpeed(speed) {
    let format = speed.toString()[0]

    $('.speedValue').text(format+"/"+"10")
    $('.speedStick').removeClass('activeValue')

    var i, toplam = 0;

    for(i = 1; i < parseInt(format)+1; i++) {
        $('.speedStick'+i).addClass('activeValue')
    }
}

function loadBreaking(data) {

    if(data.toString()[0] == "1") {
        format = 10
    }
    else {
        format = data.toString()[3]
    }

    $('.brakeValue').text(format+"/"+"10")
    $('.brakeStick').removeClass('activeValue')

    var i, toplam = 0;

    for(i = 1; i < parseInt(format)+1; i++) {
        $('.brakeStick'+i).addClass('activeValue')
    }
}

function loadTorque(torque) {

    let newTorque = parseInt(torque.toFixed(1)/500*100)

    if(newTorque.toString()[0] == "100") {
        format = 10
    }
    else {
        format = newTorque.toString()[0]
    }

    $('.torqueValue').text(format+"/"+"10")
    $('.torqueStick').removeClass('activeValue')

    var i, toplam = 0;

    for(i = 1; i < parseInt(format)+1; i++) {
        $('.torqueStick'+i).addClass('activeValue')
    }
}

function loadPower(power) {

    let newPower = parseInt(power.toFixed(1)/500*100)

    if(newPower.toString()[0] == "100") {
        format = 10
    }
    else {
        format = newPower.toString()[0]
    }

    $('.powerValue').text(format+"/"+"10")
    $('.powerStick').removeClass('activeValue')

    var i, toplam = 0;

    for(i = 1; i < parseInt(format)+1; i++) {
        $('.powerStick'+i).addClass('activeValue')
    }
}

function loadAcceleration(ace) {

    let newPower = parseInt(Math.ceil(100*ace))
    if(newPower.toString()[0] == "100") {
        format = 10
    }
    else {
        format = newPower.toString()[0]
    }

    $('.aceValue').text(format+"/"+"10")
    $('.aceStick').removeClass('activeValue')

    var i, toplam = 0;

    for(i = 1; i < parseInt(format)+1; i++) {
        $('.aceStick'+i).addClass('activeValue')
    }
}

function loadSeat(seat) {

    $('.seatValue').text(seat+"/"+"10")
    $('.seatStick').removeClass('activeValue')

    var i, toplam = 0;

    for(i = 1; i < parseInt(seat)+1; i++) {
        $('.seatStick'+i).addClass('activeValue')
    }
}

function selectVehicle(vehicle, price, available) {
    $('.vehicle').removeClass('active')
    $('.vehicle_'+vehicle).addClass('active')
    $.post('http://stg_vehicleshop/loadVehicle', JSON.stringify({
        vehicle: vehicle,
        price: price
    }));
    isAvailable = available
}

function selectCategory(id) {
    $('.category').removeClass('active')
    $('.category_'+id).addClass('active')

    $('.vehicle').remove()
    firstVehicle = false

    $.post('http://stg_vehicleshop/loadCategory', JSON.stringify({
        id: id
    }));
}

window.addEventListener("keyup", (event) => {
    event.preventDefault();
    if (event.keyCode == 27) {
        exit()
    }
})

function reset() {
    $('body').hide()
    $('.total').text(0)
    $('.category').remove()
    $('.vehicle').remove()
    $('.colorPalete').remove()

    firstCategory = false
    firstVehicle = false
}

function exit() {
    reset()
    $.post('http://stg_vehicleshop/exit', JSON.stringify({}));
}

function formatBalance(balance) {
    var formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      });
   return formatter.format(balance)   
}

$(document).ready(function () {
    $(".input").on("keyup", function () {
      var value = $(this).val().toLowerCase();
      $(".vehicle").filter(function () {
        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
      });
    });
});

function rotate(status) {
    $.post('http://stg_vehicleshop/rotate', JSON.stringify({
        status: status
    }));
}

let color = document.getElementById('colorInput');
color.addEventListener('input', function(e) {
    let newColor = hexToRgb(color.value)
    $.post('http://stg_vehicleshop/changeColor', JSON.stringify({
        r: newColor.r,
        g: newColor.g,
        b: newColor.b,
        manuel: true
    }));
});

function hexToRgb(hex) {
    var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
    } : null;
}

function buy(type) {
    $.post('http://stg_vehicleshop/buy', JSON.stringify({
        type: type
    }));
}

function testDrive() {
    $.post('http://stg_vehicleshop/testDrive', JSON.stringify({
        available: isAvailable
    }));
}
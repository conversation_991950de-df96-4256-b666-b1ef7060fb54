local QBCore = exports['qb-core']:GetCoreObject()

function notify(message)
    QBCore.Functions.Notify(getMessage(message))
end

function getMessage(message)
    return STG.Locale[message]
end

function leftNotify(msg, thisFrame, beep, duration)
    AddTextEntry('stg', msg)

	if thisFrame then
		DisplayHelpTextThisFrame('stg', false)
	else
		if beep == nil then beep = true end
		BeginTextCommandDisplayHelp('stg')
		EndTextCommandDisplayHelp(0, false, beep, duration or -1)
	end
end

STG = {}

STG.testDriveTime = 30 -- second

STG.Shops = {
	["vehicle"] = {
		carSpawnCoord = vector4(404.90100097656, -966.78149414063, -99.004096984863, 43.07537841797),
		vehicleDeliveryCoord = vector4(-48.906772613525, -1077.6535644531, 26.81402015686, 66.28524017334),
		testDriveCoord = vector3(-942.64,-3365.96,12.95),
		testDrive = true,
		camera = {
			camCoord = vector3(404.01068115234, -962.8, -98.504096984863),
			camRotation = vector3(190.00, 180.00, 15.00),
		},
		blip = { -- blip https://wiki.rage.mp/index.php?title=Blips
			label = "Vehicle Shop",
			sprite = 523,
			scale = 0.7,
			color = 3
		},
		npc = { -- NPC https://wiki.rage.mp/index.php?title=Peds
			coord = vector4(-56.776912689209,-1098.7838134766,25.422330856323,28.34657),
			model = "ig_siemonyetarian"
		}
	},
	["air"] = {
		carSpawnCoord = vector4(404.90100097656, -966.78149414063, -99.004096984863, 43.07537841797),
		vehicleDeliveryCoord = vector4(-725.44, -1443.9, 5.00052, 66.28524017334),
		testDriveCoord = vector3(-942.64,-3365.96,12.95),
		testDrive = false,
		camera = {
			camCoord = vector3(404.01068115234, -962.8, -98.504096984863),
			camRotation = vector3(190.00, 180.00, 15.00),
		},
		blip = { -- blip https://wiki.rage.mp/index.php?title=Blips
			label = "Aircraft Shop",
			sprite = 90,
			scale = 0.7,
			color = 3
		},
		npc = { -- NPC https://wiki.rage.mp/index.php?title=Peds
			coord = vector4(-941.80, -2955.5, 12.9450, 100.551186),
			model = "s_m_m_ciasec_01"
		}
	},
	["boat"] = {
		carSpawnCoord = vector4(404.90100097656, -966.78149414063, -99.004096984863, 43.07537841797),
		vehicleDeliveryCoord = vector4(-725.44, -1443.9, 5.00052, 66.28524017334),
		testDriveCoord = vector3(-942.64,-3365.96,12.95),
		testDrive = false,
		camera = {
			camCoord = vector3(404.01068115234, -962.8, -98.504096984863),
			camRotation = vector3(190.00, 180.00, 15.00),
		},
		blip = { -- blip https://wiki.rage.mp/index.php?title=Blips
			label = "Boat Shop",
			sprite = 404,
			scale = 0.7,
			color = 3
		},
		npc = { -- NPC https://wiki.rage.mp/index.php?title=Peds
			coord = vector4(-779.66, -1439.6, 0.59512, 100.0),
			model = "a_m_y_cyclist_01"
		}
	},
}

STG.Categories = {
	["sedan"] = {
		title = "Sedans",
		image = "sedan.png",
		type = "vehicle"
	},
	["truck"] = {
		title = "Trucks",
		image = "truck.png",
		type = "vehicle"
	},
	["motorcyle"] = {
		title = "Motorcyles",
		image = "motorcyle.png",
		type = "vehicle"
	},
	["bike"] = {
		title = "Bikes",
		image = "bike.png",
		type = "vehicle"
	},
	["plane"] = {
		title = "Planes",
		image = "plane.png",
		type = "air"
	},
	["helicopter"] = {
		title = "Helicopters",
		image = "helicopter.png",
		type = "air"
	},
	["sport"] = {
		title = "Sports",
		image = "sport.png",
		type = "vehicle"
	},
	["suv"] = {
		title = "SUVs",
		image = "suv.png",
		type = "vehicle"
	},
	["boat"] = {
		title = "Boats",
		image = "boat.png",
		type = "boat"
	},
}

STG.Vehicles = {
	["sedan"] = {
		["asea"] = {
			modelName = "asea",
			price = 2000
		},
		["asterope"] = {
			modelName = "asterope",
			price = 14000
		},
		["cog55"] = {
			modelName = "cog55",
			price = 15000
		},
		["cog552"] = {
			modelName = "cog552",
			price = 21000
		},
		["cognoscenti"] = {
			modelName = "cognoscenti",
			price = 23000
		},
		["emperor"] = {
			modelName = "emperor",
			price = 25000
		},
		["fugitive"] = {
			modelName = "fugitive",
			price = 28000
		},
		["glendale"] = {
			modelName = "glendale",
			price = 30000
		},
		["ingot"] = {
			modelName = "ingot",
			price = 40000
		},
	},
	["truck"] = {
		["boxville"] = {
			modelName = "boxville",
			price = 12000
		},
		["burrito3"] = {
			modelName = "burrito3",
			price = 15000
		},
		["minivan"] = {
			modelName = "minivan",
			price = 18000
		},
		["rumpo"] = {
			modelName = "rumpo",
			price = 21000
		},
		["pony"] = {
			modelName = "pony",
			price = 25000
		},
	},
	["motorcyle"] = {
		["akuma"] = {
			modelName = "akuma",
			price = 17500
		},
		["bati"] = {
			modelName = "bati",
			price = 10000
		},
		["chimera"] = {
			modelName = "chimera",
			price = 10000
		},
		["carbonrs"] = {
			modelName = "carbonrs",
			price = 10000
		},
		["enduro"] = {
			modelName = "enduro",
			price = 15000
		},
		["fcr"] = {
			modelName = "fcr",
			price = 15000
		},
		["lectro"] = {
			modelName = "lectro",
			price = 25000
		},
		["nightblade"] = {
			modelName = "nightblade",
			price = 35000
		},
		["sanctus"] = {
			modelName = "sanctus",
			price = 50000
		},
	},
	["bike"] = {
		["bmx"] = {
			modelName = "bmx",
			price = 1000
		},
		["cruiser"] = {
			modelName = "cruiser",
			price = 3000
		},
		["fixter"] = {
			modelName = "fixter",
			price = 3000
		},
		["scorcher"] = {
			modelName = "scorcher",
			price = 4000
		},
		["tribike"] = {
			modelName = "tribike",
			price = 5000
		},
		["tribike2"] = {
			modelName = "tribike2",
			price = 5000
		},
		["tribike3"] = {
			modelName = "tribike3",
			price = 5000
		},
	},
	["plane"] = {
		["duster"] = {
			modelName = "duster",
			price = 95000,
			testDrive = false,
		},
		["luxor"] = {
			modelName = "luxor",
			price = 85000,
			testDrive = false,
		},
		["shamal"] = {
			modelName = "shamal",
			price = 75000,
			testDrive = false,
		},
		["vestra"] = {
			modelName = "vestra",
			price = 55000,
			testDrive = false,
		},
	},
	["helicopter"] = {
		["buzzard"] = {
			modelName = "buzzard",
			price = 35000,
			testDrive = false,
		},
		["maverick"] = {
			modelName = "maverick",
			price = 25000,
			testDrive = false,
		},
		["havok"] = {
			modelName = "havok",
			price = 15000,
			testDrive = false,
		},
	},
	["boat"] = {
		["dinghy"] = {
			modelName = "dinghy",
			price = 35000,
			testDrive = false,
		},
		["seashark"] = {
			modelName = "seashark",
			price = 25000,
			testDrive = false,
		},
		["squalo"] = {
			modelName = "squalo",
			price = 15000,
			testDrive = false,
		},
	},
	["sport"] = {
		["banshee"] = {
			modelName = "banshee",
			price = 35000
		},
		["bestiagts"] = {
			modelName = "bestiagts",
			price = 45000
		},
		["buffalo"] = {
			modelName = "buffalo",
			price = 47000
		},
		["comet2"] = {
			modelName = "comet2",
			price = 52000
		},
		["coquette"] = {
			modelName = "coquette",
			price = 55000
		},
		["deveste"] = {
			modelName = "deveste",
			price = 70000
		},
		["elegy"] = {
			modelName = "elegy",
			price = 52000
		},
		["flashgt"] = {
			modelName = "flashgt",
			price = 65000
		},
	},
	["suv"] = {
		["baller"] = {
			modelName = "baller",
			price = 35000
		},
		["contender"] = {
			modelName = "contender",
			price = 45000
		},
		["dubsta"] = {
			modelName = "dubsta",
			price = 47000
		},
		["fq2"] = {
			modelName = "fq2",
			price = 52000
		},
		["granger"] = {
			modelName = "granger",
			price = 55000
		},
		["landstalker"] = {
			modelName = "landstalker",
			price = 70000
		},
		["patriot"] = {
			modelName = "patriot",
			price = 52000
		},
		["toros"] = {
			modelName = "toros",
			price = 65000
		},
	}
}

STG.Colors = {
	[1] = {
		r = 216,
		g = 217,
		b = 215,
	},
	[2] = {
		r = 253,
		g = 142,
		b = 39,
	},
	[3] = {
		r = 190,
		g = 237,
		b = 0,
	},
	[4] = {
		r = 119,
		g = 255,
		b = 12,
	},
	[5] = {
		r = 255,
		g = 86,
		b = 248,
	},
	[6] = {
		r = 12,
		g = 153,
		b = 255,
	},
	[7] = {
		r = 238,
		g = 3,
		b = 3,
	},
	[8] = {
		r = 255,
		g = 12,
		b = 202,
	},
	[9] = {
		r = 5,
		g = 0,
		b = 255,
	},
	[10] = {
		r = 0,
		g = 255,
		b = 133,
	},
	[11] = {
		r = 0,
		g = 255,
		b = 209,
	},
	[12] = {
		r = 255,
		g = 122,
		b = 0,
	},
}
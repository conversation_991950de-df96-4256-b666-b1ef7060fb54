<html>

<head>
    <title>stg_vehicleshop - by GENER4L</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css" type="text/css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com%22%3E/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;800&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css" integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g==" crossorigin="anonymous" referrerpolicy="no-referrer"/>
</head>

<body>
    <div oncontextmenu="rotate('right')" onclick="rotate('left')" id="rotateCheck"></div>
    <img class="effect" src="images/effect.png">
    <p class="title">VEHICLE</p>
    <p class="subTitle">DEALERSHIP</p>
    <p class="description">Welcome to our store. You can take a look at the cars here!</p>
    <img class="stick" src="images/stick.png">
    <img class="kopya" src="images/Frame 2.png">
    <input type="text" class="input" placeholder="Search...">
    <img class="search" src="images/search.png">
    <div id="categoryArea"></div>
    <div id="vehicleArea"></div>

    <p class="titleRight">VEHICLE</p>
    <p class="subTitleRight">INFORMATION</p>
    <p class="descriptionRight">For enthusiasts, here is technical information about the vehicle.</p>

    <p class="vehicleName">ASEA</p>
    <p class="vehiclePriceText">$50.000</p>

    <p class="rightTitle">Technical Information</p>
    <img class="pageStick" src="images/pageStick.png">

    <div id="infoArea">
        <div class="info">
            <p class="infoTitle">Maximum Speed</p>
            <p class="infoValue speedValue">6/10</p>
            <div class="valueBox speedStick speedStick1"></div>
            <div class="valueBox speedStick speedStick2"></div>
            <div class="valueBox speedStick speedStick3"></div>
            <div class="valueBox speedStick speedStick4"></div>
            <div class="valueBox speedStick speedStick5"></div>
            <div class="valueBox speedStick speedStick6"></div>
            <div class="valueBox speedStick speedStick7"></div>
            <div class="valueBox speedStick speedStick8"></div>
            <div class="valueBox speedStick speedStick9"></div>
            <div class="valueBox speedStick speedStick10"></div>
        </div>
        <div class="info">
            <p class="infoTitle">Brake</p>
            <p class="infoValue brakeValue">6/10</p>
            <div class="valueBox brakeStick brakeStick1"></div>
            <div class="valueBox brakeStick brakeStick2"></div>
            <div class="valueBox brakeStick brakeStick3"></div>
            <div class="valueBox brakeStick brakeStick4"></div>
            <div class="valueBox brakeStick brakeStick5"></div>
            <div class="valueBox brakeStick brakeStick6"></div>
            <div class="valueBox brakeStick brakeStick7"></div>
            <div class="valueBox brakeStick brakeStick8"></div>
            <div class="valueBox brakeStick brakeStick9"></div>
            <div class="valueBox brakeStick brakeStick10"></div>
        </div>
        <div class="info">
            <p class="infoTitle">Torque</p>
            <p class="infoValue torqueValue">6/10</p>
            <div class="valueBox torqueStick torqueStick1"></div>
            <div class="valueBox torqueStick torqueStick2"></div>
            <div class="valueBox torqueStick torqueStick3"></div>
            <div class="valueBox torqueStick torqueStick4"></div>
            <div class="valueBox torqueStick torqueStick5"></div>
            <div class="valueBox torqueStick torqueStick6"></div>
            <div class="valueBox torqueStick torqueStick7"></div>
            <div class="valueBox torqueStick torqueStick8"></div>
            <div class="valueBox torqueStick torqueStick9"></div>
            <div class="valueBox torqueStick torqueStick10"></div>
        </div>
        <div class="info">
            <p class="infoTitle">Power</p>
            <p class="infoValue powerValue">6/10</p>
            <div class="valueBox powerStick powerStick1"></div>
            <div class="valueBox powerStick powerStick2"></div>
            <div class="valueBox powerStick powerStick3"></div>
            <div class="valueBox powerStick powerStick4"></div>
            <div class="valueBox powerStick powerStick5"></div>
            <div class="valueBox powerStick powerStick6"></div>
            <div class="valueBox powerStick powerStick7"></div>
            <div class="valueBox powerStick powerStick8"></div>
            <div class="valueBox powerStick powerStick9"></div>
            <div class="valueBox powerStick powerStick10"></div>
        </div>
        <div class="info">
            <p class="infoTitle">Acceleration</p>
            <p class="infoValue aceValue">6/10</p>
            <div class="valueBox aceStick aceStick1"></div>
            <div class="valueBox aceStick aceStick2"></div>
            <div class="valueBox aceStick aceStick3"></div>
            <div class="valueBox aceStick aceStick4"></div>
            <div class="valueBox aceStick aceStick5"></div>
            <div class="valueBox aceStick aceStick6"></div>
            <div class="valueBox aceStick aceStick7"></div>
            <div class="valueBox aceStick aceStick8"></div>
            <div class="valueBox aceStick aceStick9"></div>
            <div class="valueBox aceStick aceStick10"></div>
        </div>
        <div class="info">
            <p class="infoTitle">Seats</p>
            <p class="infoValue seatValue">1/19</p>
            <div class="valueBox seatStick seatStick1"></div>
            <div class="valueBox seatStick seatStick2"></div>
            <div class="valueBox seatStick seatStick3"></div>
            <div class="valueBox seatStick seatStick4"></div>
            <div class="valueBox seatStick seatStick5"></div>
            <div class="valueBox seatStick seatStick6"></div>
            <div class="valueBox seatStick seatStick7"></div>
            <div class="valueBox seatStick seatStick8"></div>
            <div class="valueBox seatStick seatStick9"></div>
            <div class="valueBox seatStick seatStick10"></div>
        </div>
    </div>

    <p class="rightTitle color">Color palette</p>
    <img class="pageStick colorStick" src="images/pageStick.png">

    <div id="colorArea"></div>

    <input type="color" id="colorInput" name="head" value="#09ff00">

    <p class="rightTitle purchase">Purchase</p>
    <img class="pageStick purchaseStick" src="images/pageStick.png">

    <p class="totalPrice">TOTAL PRICE</p>
    <p class="total">$120.00</p>

    <img onclick="buy('bank')" class="card" src="images/card.png">
    <img onclick="buy('cash')" class="cash" src="images/money.png">

    <p onclick="testDrive()" class="testDrive">Test Drive</p>

    <div id="rightMouse">
        <img class="rightMouse" src="images/rightmouse.png">
        <p class="rotateRight">Rotate Right</p>
        <p class="rotateDescription">Right mouse button</p>
    </div>
    <div id="leftMouse">
        <img class="leftMouse" src="images/leftmouse.png">
        <p class="rotateLeft">Rotate Left</p>
        <p class="rotateLeftDescription">Right mouse button</p>
    </div>
    <script src="main.js"></script>
</body>